services:
  # Main web service
  - type: web
    name: flex-home-app
    env: docker
    dockerfilePath: ./Dockerfile
    plan: starter
    region: oregon
    branch: main
    healthCheckPath: /up
    envVars:
      - key: APP_NAME
        value: "Flex Home"
      - key: APP_ENV
        value: production
      - key: APP_DEBUG
        value: false
      - key: APP_URL
        fromService:
          type: web
          name: flex-home-app
          property: host
      - key: LOG_CHANNEL
        value: stderr
      - key: LOG_LEVEL
        value: error
      - key: BROADCAST_DRIVER
        value: log
      - key: CACHE_STORE
        value: file
      - key: QUEUE_CONNECTION
        value: database
      - key: SESSION_DRIVER
        value: file
      - key: SESSION_LIFETIME
        value: 120
      - key: DB_CONNECTION
        value: mysql
      - key: DB_HOST
        fromDatabase:
          name: flex-home-db
          property: host
      - key: DB_PORT
        fromDatabase:
          name: flex-home-db
          property: port
      - key: DB_DATABASE
        fromDatabase:
          name: flex-home-db
          property: database
      - key: DB_USERNAME
        fromDatabase:
          name: flex-home-db
          property: user
      - key: DB_PASSWORD
        fromDatabase:
          name: flex-home-db
          property: password
      - key: ADMIN_DIR
        value: admin
      - key: CMS_ENABLE_INSTALLER
        value: false
      - key: FORCE_SCHEMA
        value: https
      - key: FORCE_ROOT_URL
        fromService:
          type: web
          name: flex-home-app
          property: host

databases:
  - name: flex-home-db
    databaseName: flex_home_production
    user: flex_home_user
    plan: starter
