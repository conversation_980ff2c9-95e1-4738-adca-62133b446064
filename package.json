{"private": true, "workspaces": {"packages": ["platform/core/*", "platform/packages/*", "platform/plugins/*", "platform/themes/*"]}, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "format": "npx prettier platform/**/resources/js/**/*.{js,vue} platform/**/resources/sass/*.scss platform/**/resources/views/**/*.blade.php platform/themes/*/{layouts,partials,views,widgets}/**/*.blade.php platform/themes/*/assets/**/*.{js,scss} --write"}, "devDependencies": {"@fullhuman/postcss-purgecss": "^6.0.0", "@shufo/prettier-plugin-blade": "^1.15.3", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "glob": "^11.0.2", "laravel-mix": "^6.0.49", "postcss": "^8.5.3", "prettier": "^3.5.3", "resolve-url-loader": "^5.0.0", "sass": "^1.87.0", "sass-loader": "^16.0.5", "vue-loader": "^17.3.0"}, "dependencies": {"axios": "^1.9.0", "bootstrap": "^5.3.5", "cropperjs": "^2.0.0", "epic-spinners": "^2.0.0", "jquery": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.30.1", "sanitize-html": "^2.16.0", "tiny-emitter": "^2.1.0", "vue": "^3.3.4"}}