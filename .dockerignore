# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
*.md

# Development files
.env.example
.env.local
.env.development
docker-compose.yml
docker-compose.override.yml

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Laravel specific
/storage/logs/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/debugbar/*
/bootstrap/cache/*

# Keep important storage directories but ignore contents
/storage/logs/.gitkeep
/storage/framework/cache/.gitkeep
/storage/framework/sessions/.gitkeep
/storage/framework/views/.gitkeep

# Testing
/tests
phpunit.xml
.phpunit.result.cache

# Development tools
.php_cs.cache
.php-cs-fixer.cache
.phpstan.neon
.phpstan-baseline.neon

# Deployment scripts (keep deploy.sh but ignore others)
*.sh
!deploy.sh

# Temporary files
*.tmp
*.temp
*.log

# Vendor (will be installed during build)
# vendor

# Public storage (will be linked during deployment)
/public/storage

# Cache files
.cache
*.cache
