[PHP]
; Performance settings
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
post_max_size = 100M
upload_max_filesize = 100M
max_file_uploads = 20

; Error reporting (production)
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

; Session settings
session.cookie_httponly = On
session.cookie_secure = On
session.use_strict_mode = On

; Security settings
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; OPcache settings
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
opcache.validate_timestamps = 0

; Timezone
date.timezone = UTC

; File uploads
file_uploads = On

; Realpath cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600
