import NProgress from 'nprogress/nprogress'

/**
 * @param {string} color
 * @returns {void}
 */
function injectCSS(color) {
    const element = document.createElement('style')
    element.textContent = `
        #nprogress {
          pointer-events: none;
        }

        #nprogress .bar {
          background: ${color};

          position: fixed;
          z-index: 1031;
          top: 0;
          left: 0;

          width: 100%;
          height: 2px;
        }

        #nprogress .peg {
          display: block;
          position: absolute;
          right: 0px;
          width: 100px;
          height: 100%;
          box-shadow: 0 0 10px ${color}, 0 0 5px ${color};
          opacity: 1.0;

          -webkit-transform: rotate(3deg) translate(0px, -4px);
              -ms-transform: rotate(3deg) translate(0px, -4px);
                  transform: rotate(3deg) translate(0px, -4px);
        }

        #nprogress .spinner {
          display: block;
          position: fixed;
          z-index: 1031;
          top: 15px;
          right: 15px;
        }

        #nprogress .spinner-icon {
          width: 18px;
          height: 18px;
          box-sizing: border-box;

          border: solid 2px transparent;
          border-top-color: ${color};
          border-left-color: ${color};
          border-radius: 50%;

          -webkit-animation: nprogress-spinner 400ms linear infinite;
                  animation: nprogress-spinner 400ms linear infinite;
        }

        .nprogress-custom-parent {
          overflow: hidden;
          position: relative;
        }

        .nprogress-custom-parent #nprogress .spinner,
        .nprogress-custom-parent #nprogress .bar {
          position: absolute;
        }

        @-webkit-keyframes nprogress-spinner {
          0%   { -webkit-transform: rotate(0deg); }
          100% { -webkit-transform: rotate(360deg); }
        }
        @keyframes nprogress-spinner {
          0%   { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
    `
    document.head.appendChild(element)
}

injectCSS('#007bff')

/**
 * @param {number} delay
 * @param {string} color
 * @param {boolean} includeCSS
 * @param {boolean} showSpinner
 * @returns {void}
 */
export default function setupProgress({
    delay = 250,
    color = 'var(--bb-primary)',
    includeCSS = true,
    showSpinner = false,
} = {}) {
    $(document).on('ajaxSend', () => NProgress.inc(delay))
    $(document).on('ajaxStop', () => NProgress.done())

    $httpClient.beforeSend(() => NProgress.inc(delay))
    $httpClient.completed(() => NProgress.done())

    NProgress.configure({ showSpinner })

    if (includeCSS) {
        injectCSS(color)
    }
}
