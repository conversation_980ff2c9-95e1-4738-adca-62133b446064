.gallery-images-wrapper {
    border-radius: var(--bb-border-radius);

    .list-gallery-media-images {

        .gallery-image-item-handler {
            position: relative;

            .list-photo-hover-overlay {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translateY(-50%) translateX(-50%);
                z-index: 10;

                .photo-overlay-actions {
                    list-style: none;
                    padding: 0;
                    display: flex;
                    gap: 10px;

                    li {
                        a {
                            color: white;
                        }
                    }
                }
            }
        }
    }
}

.preview-image-wrapper {
    overflow: hidden;
    border: 1px solid var(--bb-border-color);
    border-radius: 8px;

    .preview-image-inner {
        position: relative;
        width: 100%;
        overflow: hidden;
        padding-top: 100%;

        img.default-image {
            padding: 10px;
        }

        .preview-image {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-box-actions {
            width: 100%;
            height: 100%;
            position: absolute;
            inset: 0;
            z-index: 11;

            a {
                cursor: pointer;
            }
        }

        .image-picker-backdrop {
            transition: .3s;
            position:absolute;
            inset: 0;
        }

        &:hover {
            .image-picker-backdrop {
                z-index: 10;
                background-color: rgba($color: #000, $alpha: .5);
                height: 100%;
                width: 100%;
            }
        }

        .image-picker-remove-button {
            position: absolute;
            z-index: 12;
            top: 5px;
            right: 5px;

            button {
                height: 20px;
                width: 20px;
                border-radius: 50%;
            }
        }
    }
}
