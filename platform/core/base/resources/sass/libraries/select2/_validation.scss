.select2-container--default {
    .is-valid + &,
    .was-validated select:valid + & {
        .select2-selection {
            border-color: $s2bs5-valid-border-color;
        }

        &.select2-container--focus,
        &.select2-container--open {
            .select2-selection {
                border-color: $s2bs5-valid-border-color;
                box-shadow: $s2bs5-valid-focus-box-shadow;
            }
        }

        &.select2-container--open {
            &.select2-container--below .select2-selection {
                border-bottom: 0 solid transparent;
            }

            &.select2-container--above .select2-selection {
                border-top: 0 solid transparent;
                @include border-top-radius(0);
            }
        }
    }

    .is-invalid + &,
    .was-validated select:invalid + & {
        .select2-selection {
            border-color: $s2bs5-invalid-border-color;
        }

        &.select2-container--focus,
        &.select2-container--open {
            .select2-selection {
                border-color: $s2bs5-invalid-border-color;
                box-shadow: $s2bs5-invalid-focus-box-shadow;
            }
        }

        &.select2-container--open {
            &.select2-container--below .select2-selection {
                border-bottom: 0 solid transparent;
            }

            &.select2-container--above .select2-selection {
                border-top: 0 solid transparent;
                @include border-top-radius(0);
            }
        }
    }
}
