<?php

namespace Database\Seeders;

use Botble\ACL\Database\Seeders\UserSeeder;
use Botble\Base\Events\SeederPrepared;
use Botble\Base\Supports\BaseSeeder;
use Botble\Base\Supports\Database;
use Botble\Language\Database\Seeders\LanguageSeeder;
use Bo<PERSON>ble\RealEstate\Database\Seeders\CurrencySeeder;

class DatabaseSeeder extends BaseSeeder
{
    public function run(): void
    {
        SeederPrepared::dispatch();

        Database::restoreFromPath(base_path('database.sql'));

        $this->command->call('migrate');

        $this->uploadFiles('banner');
        $this->uploadFiles('cities');
        $this->uploadFiles('locations');
        $this->uploadFiles('logo');

        $this->call([
            LanguageSeeder::class,
            CurrencySeeder::class,
            CategorySeeder::class,
            FacilitySeeder::class,
            FeatureSeeder::class,
            PackageSeeder::class,
            AccountSeeder::class,
            UserSeeder::class,
            SettingSeeder::class,
            PageSeeder::class,
            LatLongSeeder::class,
            MenuSeeder::class,
            ThemeOptionSeeder::class,
            BlogSeeder::class,
            ProjectSeeder::class,
            PropertySeeder::class,
            ReviewSeeder::class,
            AnnouncementSeeder::class,
            WidgetSeeder::class,
        ]);

        $this->finished();
    }
}
