<?php

namespace Database\Seeders;

use <PERSON><PERSON>ble\ACL\Models\User;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Base\Models\MetaBox;
use <PERSON><PERSON>ble\Base\Supports\BaseSeeder;
use Bo<PERSON>ble\Blog\Database\Traits\HasBlogSeeder;
use Botble\Blog\Models\Category;
use Botble\Blog\Models\Post;
use Botble\Blog\Models\Tag;
use Botble\Media\Facades\RvMedia;
use Botble\Slug\Facades\SlugHelper;
use Botble\Slug\Models\Slug;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class BlogSeeder extends BaseSeeder
{
    use HasBlogSeeder;

    public function run(): void
    {
        $this->uploadFiles('news');
        $this->uploadFiles('posts');

        Post::query()->where('id', '>', 4)->delete();
        Category::query()->truncate();
        Tag::query()->where('id', '>', 3)->delete();
        DB::table('post_categories')->truncate();
        DB::table('post_tags')->truncate();

        Slug::query()->where('reference_type', Post::class)->where('reference_id', '>', 4)->delete();
        Slug::query()->where('reference_type', Tag::class)->where('reference_id', '>', 3)->delete();
        Slug::query()->where('reference_type', Category::class)->truncate();
        MetaBox::query()->where('reference_type', Post::class)->where('reference_id', '>', 4)->delete();
        MetaBox::query()->where('reference_type', Tag::class)->where('reference_id', '>', 3)->delete();
        MetaBox::query()->where('reference_type', Category::class)->where('reference_id', '>', 8)->delete();

        $posts = [
            [
                'name' => 'The Top 2020 Handbag Trends to Know',
            ],
            [
                'name' => 'Top Search Engine Optimization Strategies!',
            ],
            [
                'name' => 'Which Company Would You Choose?',
            ],
            [
                'name' => 'Used Car Dealer Sales Tricks Exposed',
            ],
            [
                'name' => '20 Ways To Sell Your Product Faster',
            ],
            [
                'name' => 'The Secrets Of Rich And Famous Writers',
            ],
            [
                'name' => 'Imagine Losing 20 Pounds In 14 Days!',
            ],
            [
                'name' => 'Are You Still Using That Slow, Old Typewriter?',
            ],
            [
                'name' => 'A Skin Cream That’s Proven To Work',
            ],
            [
                'name' => '10 Reasons To Start Your Own, Profitable Website!',
            ],
            [
                'name' => 'Simple Ways To Reduce Your Unwanted Wrinkles!',
            ],
            [
                'name' => 'Apple iMac with Retina 5K display review',
            ],
            [
                'name' => '10,000 Web Site Visitors In One Month:Guaranteed',
            ],
            [
                'name' => 'Unlock The Secrets Of Selling High Ticket Items',
            ],
            [
                'name' => '4 Expert Tips On How To Choose The Right Men’s Wallet',
            ],
            [
                'name' => 'Sexy Clutches: How to Buy & Wear a Designer Clutch Bag',
            ],
        ];

        $faker = $this->fake();

        $this->createBlogCategories([
            [
                'name' => 'News',
                'description' => $faker->text(),
                'is_featured' => 1,
            ],
            [
                'name' => 'House architecture',
                'description' => $faker->text(),
                'is_featured' => 1,
            ],
            [
                'name' => 'House design',
                'description' => $faker->text(),
                'is_featured' => 1,
            ],
            [
                'name' => 'Building materials',
                'description' => $faker->text(),
                'is_featured' => 1,
            ],
        ]);

        $categories = Category::query()->get();

        foreach ($posts as $index => $item) {
            $item['content'] =
                ($index % 3 == 0 ? Html::tag(
                    'p',
                    '[youtube-video]https://www.youtube.com/watch?v=SlPhMPnQ58k[/youtube-video]'
                ) : '') .
                Html::tag('p', $faker->realText(1000)) .
                Html::tag(
                    'p',
                    Html::image(RvMedia::getImageUrl('news/' . $faker->numberBetween(1, 5) . '.jpg'))
                        ->toHtml(),
                    ['class' => 'text-center']
                ) .
                Html::tag('p', $faker->realText(500)) .
                Html::tag(
                    'p',
                    Html::image(RvMedia::getImageUrl('news/' . $faker->numberBetween(6, 10) . '.jpg'))
                        ->toHtml(),
                    ['class' => 'text-center']
                ) .
                Html::tag('p', $faker->realText(1000)) .
                Html::tag(
                    'p',
                    Html::image(RvMedia::getImageUrl('news/' . $faker->numberBetween(11, 14) . '.jpg'))
                        ->toHtml(),
                    ['class' => 'text-center']
                ) .
                Html::tag('p', $faker->realText(1000));
            $item['author_id'] = User::query()->value('id');
            $item['author_type'] = User::class;
            $item['views'] = $faker->numberBetween(100, 2500);
            $item['is_featured'] = $index < 9;
            $item['image'] = 'news/' . ($index + 1) . '.jpg';
            $item['description'] = $faker->text();
            $item['content'] = str_replace(url(''), '', $item['content']);

            $post = Post::query()->create($item);

            $post->categories()->sync($categories->random(2)->pluck('id')->toArray());

            $post->tags()->sync([1, 2, 3]);

            SlugHelper::createSlug($post);
        }

        Post::query()->update(['created_at' => Carbon::now(), 'updated_at' => Carbon::now()]);
    }
}
